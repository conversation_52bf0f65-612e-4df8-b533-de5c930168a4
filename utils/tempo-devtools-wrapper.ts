import { Platform } from "react-native";

/**
 * Safe wrapper for tempo-devtools that only loads in web environment
 * This prevents "document is not defined" errors in React Native
 */
export const initTempoDevtools = () => {
  // Only initialize in web environment with proper browser APIs
  if (
    process.env.EXPO_PUBLIC_TEMPO === "true" &&
    Platform.OS === "web" &&
    typeof window !== "undefined" &&
    typeof document !== "undefined"
  ) {
    try {
      // Dynamic import to prevent loading in non-web environments
      const tempoDevtools = require("tempo-devtools");
      if (tempoDevtools && tempoDevtools.TempoDevtools) {
        tempoDevtools.TempoDevtools.init();
        console.log("TempoDevtools initialized successfully");
      }
    } catch (error) {
      console.warn("TempoDevtools initialization failed:", error);
    }
  } else {
    console.log("TempoDevtools skipped - not in web environment or not enabled");
  }
};

/**
 * Mock implementation for non-web environments
 */
export const TempoDevtools = {
  init: () => {
    if (Platform.OS !== "web") {
      console.log("TempoDevtools mock - not available in mobile environment");
      return;
    }
    initTempoDevtools();
  },
};
