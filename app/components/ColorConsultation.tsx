import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Image,
  Alert,
  TextInput,
} from "react-native";
import {
  Camera,
  ArrowRight,
  Check,
  X,
  Upload,
  RefreshCw,
  ImageIcon,
  Shield,
  Save,
  ArrowLeft,
} from "lucide-react-native";
import { useRouter } from "expo-router";
import AsyncStorage from "@react-native-async-storage/async-storage";
import * as ImagePicker from "expo-image-picker";
import * as MediaLibrary from "expo-media-library";
import DiagnosisResults from "./DiagnosisResults";

interface Client {
  id: string;
  name: string;
  image?: string;
  lastVisit?: string;
  email?: string;
  phone?: string;
}

interface DiagnosisData {
  naturalLevel: number;
  undertone: "warm" | "cool" | "neutral";
  porosity: "low" | "medium" | "high";
  condition: "healthy" | "normal" | "damaged" | "severely_damaged";
  canasPercentage: number;
  thickness: string;
  density: string;
  elasticity: string;
  previousTreatments: string[];
  analysisConfidence: number;
  analysisNotes: string[];
  timestamp: string;
}

interface Formula {
  products: Array<{ name: string; amount: string; brand?: string }>;
  instructions: string;
  processingTime: number;
  aftercare: string[];
  difficulty: "easy" | "medium" | "advanced";
  estimatedCost?: number;
  warnings?: string[];
}

interface ConsultationData {
  id: string;
  client: Client;
  diagnosis: DiagnosisData;
  colorGoal: string;
  formula: Formula;
  hairImages: string[];
  referenceImages: string[];
  timestamp: string;
  status: "draft" | "completed" | "applied";
  notes?: string;
}

interface ColorConsultationProps {
  onComplete?: (data: ConsultationData) => void;
  selectedClient?: Client;
  consultationId?: string; // Para editar consultas existentes
}

const ColorConsultation = ({
  onComplete = () => {},
  selectedClient = {
    id: "1",
    name: "Ana García",
    image: "https://api.dicebear.com/7.x/avataaars/svg?seed=Ana",
    lastVisit: "15 Oct 2023",
  },
  consultationId,
}: ColorConsultationProps) => {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const [hairImages, setHairImages] = useState<string[]>([]);
  const [referenceImages, setReferenceImages] = useState<string[]>([]);
  const [diagnosisData, setDiagnosisData] = useState<DiagnosisData | null>(null);
  const [colorGoal, setColorGoal] = useState("");
  const [formula, setFormula] = useState<Formula | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [consultationNotes, setConsultationNotes] = useState("");
  const [isEditingGoal, setIsEditingGoal] = useState(false);

  // Cargar consulta existente si se proporciona consultationId
  useEffect(() => {
    if (consultationId) {
      loadExistingConsultation(consultationId);
    }
  }, [consultationId]);

  // Auto-guardar borrador cada 30 segundos
  useEffect(() => {
    const interval = setInterval(() => {
      saveDraft();
    }, 30000);

    return () => clearInterval(interval);
  }, [hairImages, referenceImages, diagnosisData, colorGoal, formula]);

  const loadExistingConsultation = async (id: string) => {
    try {
      const stored = await AsyncStorage.getItem(`consultation_${id}`);
      if (stored) {
        const consultation: ConsultationData = JSON.parse(stored);
        setHairImages(consultation.hairImages);
        setReferenceImages(consultation.referenceImages);
        setDiagnosisData(consultation.diagnosis);
        setColorGoal(consultation.colorGoal);
        setFormula(consultation.formula);
        setConsultationNotes(consultation.notes || "");

        // Determinar el paso actual basado en los datos disponibles
        if (consultation.formula) setCurrentStep(4);
        else if (consultation.colorGoal) setCurrentStep(3);
        else if (consultation.diagnosis) setCurrentStep(2);
        else setCurrentStep(1);
      }
    } catch (error) {
      console.error("Error loading consultation:", error);
      Alert.alert("Error", "No se pudo cargar la consulta existente");
    }
  };

  const saveDraft = async () => {
    if (!hairImages.length && !referenceImages.length && !colorGoal) return;

    try {
      const draftId = consultationId || `draft_${Date.now()}`;
      const consultationData: ConsultationData = {
        id: draftId,
        client: selectedClient,
        diagnosis: diagnosisData!,
        colorGoal,
        formula: formula!,
        hairImages,
        referenceImages,
        timestamp: new Date().toISOString(),
        status: "draft",
        notes: consultationNotes,
      };

      await AsyncStorage.setItem(`consultation_${draftId}`, JSON.stringify(consultationData));
      console.log("Draft saved automatically");
    } catch (error) {
      console.error("Error saving draft:", error);
    }
  };

  // Mock data for diagnosis results
  const mockDiagnosisData = {
    naturalLevel: 6,
    undertone: "Warm",
    porosity: "Medium",
    condition: "Good",
    canasPercentage: 20,
    thickness: "Medium",
    density: "High",
    elasticity: "Normal",
    previousTreatments: ["Balayage (3 months ago)", "Keratin (1 month ago)"],
  };

  // Mock data for formula
  const mockFormula = {
    products: [
      { name: "Base Color 7N", amount: "30g" },
      { name: "Developer 20 Vol", amount: "30g" },
      { name: "Toner 9V", amount: "15g" },
    ],
    instructions:
      "Apply base color to roots first. Process for 35 minutes. Rinse thoroughly. Apply toner to mid-lengths and ends. Process for 20 minutes.",
    processingTime: 55,
    aftercare: ["Hydrating Mask", "Color Protection Shampoo"],
  };

  const requestPermissions = async () => {
    const { status: cameraStatus } =
      await ImagePicker.requestCameraPermissionsAsync();
    const { status: mediaLibraryStatus } =
      await ImagePicker.requestMediaLibraryPermissionsAsync();

    if (cameraStatus !== "granted" || mediaLibraryStatus !== "granted") {
      Alert.alert(
        "Permissions Required",
        "Camera and photo library access are required to capture and select hair images for AI analysis.",
        [{ text: "OK" }],
      );
      return false;
    }
    return true;
  };

  const simulateFaceBlurring = (imageUri: string) => {
    // In a real implementation, this would use AI/ML to detect and blur faces
    // For now, we'll simulate this process and return the original image
    // The actual face blurring would happen on the server or using a local ML model
    console.log("Face blurring applied to:", imageUri);
    return imageUri;
  };

  const handleCaptureImage = async () => {
    const hasPermissions = await requestPermissions();
    if (!hasPermissions) return;

    Alert.alert(
      "Add Hair Photo",
      "Choose how you want to add a photo for AI hair analysis",
      [
        {
          text: "Take Photo",
          onPress: () => captureFromCamera(),
        },
        {
          text: "Choose from Gallery",
          onPress: () => selectFromGallery(),
        },
        {
          text: "Cancel",
          style: "cancel",
        },
      ],
    );
  };

  const captureFromCamera = async () => {
    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
        exif: false, // Remove EXIF data for privacy
      });

      if (!result.canceled && result.assets[0]) {
        const imageUri = result.assets[0].uri;
        // Apply face blurring for privacy
        const blurredImageUri = simulateFaceBlurring(imageUri);
        setHairImages([...hairImages, blurredImageUri]);

        // Show confirmation that face blurring was applied
        Alert.alert(
          "Photo Added",
          "Hair photo captured successfully. Face automatically blurred for privacy protection.",
          [{ text: "OK" }],
        );
      }
    } catch (error) {
      Alert.alert("Error", "Failed to capture image. Please try again.");
      console.error("Camera error:", error);
    }
  };

  const selectFromGallery = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
        exif: false, // Remove EXIF data for privacy
      });

      if (!result.canceled && result.assets[0]) {
        const imageUri = result.assets[0].uri;
        // Apply face blurring for privacy
        const blurredImageUri = simulateFaceBlurring(imageUri);
        setHairImages([...hairImages, blurredImageUri]);

        // Show confirmation that face blurring was applied
        Alert.alert(
          "Photo Added",
          "Hair photo selected successfully. Face automatically blurred for privacy protection.",
          [{ text: "OK" }],
        );
      }
    } catch (error) {
      Alert.alert("Error", "Failed to select image. Please try again.");
      console.error("Gallery error:", error);
    }
  };

  const handleUploadReferenceImage = async () => {
    const hasPermissions = await requestPermissions();
    if (!hasPermissions) return;

    Alert.alert(
      "Add Reference Photo",
      "Choose how you want to add a reference photo",
      [
        {
          text: "Take Photo",
          onPress: () => captureReferenceFromCamera(),
        },
        {
          text: "Choose from Gallery",
          onPress: () => selectReferenceFromGallery(),
        },
        {
          text: "Cancel",
          style: "cancel",
        },
      ],
    );
  };

  const captureReferenceFromCamera = async () => {
    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
        exif: false,
      });

      if (!result.canceled && result.assets[0]) {
        const imageUri = result.assets[0].uri;
        setReferenceImages([...referenceImages, imageUri]);
        Alert.alert(
          "Reference Photo Added",
          "Reference photo captured successfully.",
        );
      }
    } catch (error) {
      Alert.alert(
        "Error",
        "Failed to capture reference image. Please try again.",
      );
      console.error("Reference camera error:", error);
    }
  };

  const selectReferenceFromGallery = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
        exif: false,
      });

      if (!result.canceled && result.assets[0]) {
        const imageUri = result.assets[0].uri;
        setReferenceImages([...referenceImages, imageUri]);
        Alert.alert(
          "Reference Photo Added",
          "Reference photo selected successfully.",
        );
      }
    } catch (error) {
      Alert.alert(
        "Error",
        "Failed to select reference image. Please try again.",
      );
      console.error("Reference gallery error:", error);
    }
  };

  const handleAnalyzeHair = async () => {
    if (hairImages.length === 0) {
      Alert.alert("Error", "Por favor, añade al menos una imagen para el análisis");
      return;
    }

    setIsLoading(true);
    setCurrentStep(1.5); // Show loading state

    try {
      // Simular tiempo de procesamiento realista (2-4 segundos)
      const processingTime = 2000 + Math.random() * 2000;
      await new Promise((resolve) => setTimeout(resolve, processingTime));

      // Realizar análisis IA simulado
      const analysisResult = await simulateAIAnalysis(hairImages);

      setDiagnosisData(analysisResult);
      setCurrentStep(2);

      // Guardar progreso automáticamente
      await saveDraft();

      // Mostrar resultado del análisis
      Alert.alert(
        "Análisis Completado",
        `Análisis IA completado con ${analysisResult.analysisConfidence}% de confianza. Revisa los resultados y confirma antes de continuar.`,
        [{ text: "Ver Resultados", style: "default" }]
      );

    } catch (error) {
      console.error("Error during hair analysis:", error);
      Alert.alert(
        "Error de Análisis",
        "Hubo un problema durante el análisis. Por favor, inténtalo de nuevo.",
        [
          { text: "Reintentar", onPress: () => handleAnalyzeHair() },
          { text: "Cancelar", style: "cancel" }
        ]
      );
      setCurrentStep(1);
    } finally {
      setIsLoading(false);
    }
  };

  const simulateAIAnalysis = async (images: string[]): Promise<DiagnosisData> => {
    const imageCount = images.length;

    // Simulación más realista basada en diferentes escenarios
    const analysisVariations: Omit<DiagnosisData, 'analysisConfidence' | 'analysisNotes' | 'timestamp'>[] = [
      {
        naturalLevel: 4,
        undertone: "cool",
        porosity: "low",
        condition: "healthy",
        canasPercentage: 5,
        thickness: "Fine",
        density: "Medium",
        elasticity: "Excellent",
        previousTreatments: ["Virgin hair - no previous chemical treatments"],
      },
      {
        naturalLevel: 6,
        undertone: "warm",
        porosity: "medium",
        condition: "normal",
        canasPercentage: 20,
        thickness: "Medium",
        density: "High",
        elasticity: "Good",
        previousTreatments: [
          "Balayage (3 months ago)",
          "Keratin treatment (1 month ago)",
        ],
      },
      {
        naturalLevel: 8,
        undertone: "neutral",
        porosity: "high",
        condition: "damaged",
        canasPercentage: 35,
        thickness: "Coarse",
        density: "Low",
        elasticity: "Poor",
        previousTreatments: [
          "Bleaching (2 months ago)",
          "Permanent color (1 month ago)",
          "Chemical straightening (6 months ago)",
        ],
      },
      {
        naturalLevel: 3,
        undertone: "warm",
        porosity: "low",
        condition: "healthy",
        canasPercentage: 60,
        thickness: "Medium",
        density: "Medium",
        elasticity: "Good",
        previousTreatments: [
          "Henna treatment (6 months ago)",
          "Regular color touch-ups",
        ],
      },
      {
        naturalLevel: 7,
        undertone: "cool",
        porosity: "high",
        condition: "severely_damaged",
        canasPercentage: 10,
        thickness: "Fine",
        density: "Low",
        elasticity: "Poor",
        previousTreatments: [
          "Multiple bleaching sessions",
          "Chemical relaxer (4 months ago)",
          "Daily heat styling",
        ],
      },
    ];

    // Seleccionar análisis basado en número de imágenes y variación aleatoria
    const baseIndex = Math.min(imageCount - 1, analysisVariations.length - 1);
    const randomVariation = Math.floor(Math.random() * analysisVariations.length);
    const selectedAnalysis = analysisVariations[Math.max(baseIndex, randomVariation)];

    // Calcular confianza basada en número de imágenes y calidad simulada
    const baseConfidence = imageCount >= 3 ? 95 : imageCount >= 2 ? 85 : 70;
    const qualityAdjustment = Math.floor(Math.random() * 10) - 5; // ±5%
    const finalConfidence = Math.max(60, Math.min(99, baseConfidence + qualityAdjustment));

    // Generar notas de análisis más detalladas
    const analysisNotes = [
      `Análisis basado en ${imageCount} imagen${imageCount > 1 ? "es" : ""}`,
      finalConfidence >= 90
        ? "Confianza muy alta - análisis completo de múltiples ángulos"
        : finalConfidence >= 80
        ? "Buena confianza - datos de imagen suficientes"
        : "Confianza moderada - se recomiendan imágenes adicionales",
      "Rostros automáticamente difuminados para protección de privacidad",
      "Análisis de textura y distribución de color completado",
      selectedAnalysis.condition === "damaged" || selectedAnalysis.condition === "severely_damaged"
        ? "⚠️ Cabello dañado detectado - se requiere tratamiento previo"
        : "✓ Condición del cabello permite coloración directa",
      selectedAnalysis.porosity === "high"
        ? "⚠️ Alta porosidad - ajustar tiempos de procesamiento"
        : selectedAnalysis.porosity === "low"
        ? "ℹ️ Baja porosidad - puede requerir calor adicional"
        : "✓ Porosidad normal - procesamiento estándar",
    ];

    return {
      ...selectedAnalysis,
      analysisConfidence: finalConfidence,
      analysisNotes,
      timestamp: new Date().toISOString(),
    };
  };

  const generateIntelligentFormula = (diagnosis: DiagnosisData, goal: string): Formula => {
    // Simulación inteligente de formulación basada en diagnóstico y objetivo
    const { naturalLevel, undertone, porosity, condition, canasPercentage } = diagnosis;

    // Determinar productos base según el nivel natural y objetivo
    const products = [];
    const warnings = [];
    let difficulty: "easy" | "medium" | "advanced" = "medium";
    let processingTime = 35;
    let estimatedCost = 45;

    // Lógica de formulación basada en el diagnóstico
    if (condition === "severely_damaged") {
      warnings.push("⚠️ Cabello severamente dañado - realizar tratamiento reconstructor antes de colorar");
      products.push({ name: "Tratamiento Reconstructor", amount: "20ml", brand: "Olaplex" });
      difficulty = "advanced";
      processingTime += 20;
      estimatedCost += 25;
    }

    // Selección de base según nivel natural
    if (naturalLevel <= 3) {
      products.push({ name: "Base Color 4N", amount: "30g", brand: "L'Oréal" });
      products.push({ name: "Developer 20 Vol", amount: "30g" });
    } else if (naturalLevel <= 6) {
      products.push({ name: "Base Color 6N", amount: "30g", brand: "L'Oréal" });
      products.push({ name: "Developer 30 Vol", amount: "30g" });
    } else {
      products.push({ name: "Base Color 8N", amount: "25g", brand: "L'Oréal" });
      products.push({ name: "Developer 20 Vol", amount: "25g" });
    }

    // Ajustes según subtono
    if (undertone === "warm") {
      products.push({ name: "Toner Dorado 7G", amount: "15g", brand: "Wella" });
    } else if (undertone === "cool") {
      products.push({ name: "Toner Ceniza 7A", amount: "15g", brand: "Wella" });
    } else {
      products.push({ name: "Toner Natural 7N", amount: "15g", brand: "Wella" });
    }

    // Ajustes por porosidad
    if (porosity === "high") {
      products.push({ name: "Protein Filler", amount: "10ml", brand: "Redken" });
      warnings.push("⚠️ Alta porosidad - aplicar protein filler antes del color");
      processingTime -= 5;
    } else if (porosity === "low") {
      warnings.push("ℹ️ Baja porosidad - aplicar calor durante el procesamiento");
      processingTime += 10;
    }

    // Canas
    if (canasPercentage > 50) {
      products.push({ name: "Cobertura Canas Extra", amount: "10g", brand: "Matrix" });
      difficulty = "advanced";
      processingTime += 15;
    }

    // Instrucciones detalladas
    const instructions = `
1. ${condition === "severely_damaged" ? "Aplicar tratamiento reconstructor y procesar 15 min. Enjuagar." : "Preparar el cabello con champú clarificante."}
2. ${porosity === "high" ? "Aplicar protein filler en cabello húmedo." : "Secar el cabello hasta 80%."}
3. Mezclar base color con developer en proporción 1:1.
4. Aplicar primero en raíces, procesar ${Math.floor(processingTime * 0.6)} minutos.
5. Aplicar en medios y puntas, procesar ${Math.floor(processingTime * 0.4)} minutos adicionales.
6. Enjuagar completamente con agua tibia.
7. Aplicar toner y procesar 15-20 minutos.
8. Enjuagar y aplicar tratamiento acondicionador.
    `.trim();

    const aftercare = [
      "Champú sin sulfatos para color",
      "Mascarilla hidratante 2x por semana",
      condition === "damaged" || condition === "severely_damaged"
        ? "Tratamiento reconstructor semanal"
        : "Aceite protector para puntas",
      "Protector térmico antes del peinado",
      "Evitar agua muy caliente al lavar",
    ];

    return {
      products,
      instructions,
      processingTime,
      aftercare,
      difficulty,
      estimatedCost,
      warnings: warnings.length > 0 ? warnings : undefined,
    };
  };

  const handleGenerateFormula = async () => {
    if (!diagnosisData) {
      Alert.alert("Error", "Se requiere un diagnóstico antes de generar la fórmula");
      return;
    }

    if (!colorGoal && referenceImages.length === 0) {
      Alert.alert("Error", "Por favor, define el objetivo de color o añade imágenes de referencia");
      return;
    }

    setIsLoading(true);

    try {
      // Simular tiempo de procesamiento de IA
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Generar fórmula inteligente basada en diagnóstico
      const generatedFormula = generateIntelligentFormula(diagnosisData, colorGoal);

      setFormula(generatedFormula);
      setCurrentStep(4);

      // Guardar progreso
      await saveDraft();

      // Mostrar advertencias si las hay
      if (generatedFormula.warnings && generatedFormula.warnings.length > 0) {
        Alert.alert(
          "Advertencias Importantes",
          generatedFormula.warnings.join("\n\n"),
          [{ text: "Entendido", style: "default" }]
        );
      }

    } catch (error) {
      console.error("Error generating formula:", error);
      Alert.alert("Error", "No se pudo generar la fórmula. Inténtalo de nuevo.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleComplete = async () => {
    if (!diagnosisData || !formula) {
      Alert.alert("Error", "Consulta incompleta. Asegúrate de completar todos los pasos.");
      return;
    }

    try {
      setIsLoading(true);

      // Crear datos completos de la consulta
      const consultationData: ConsultationData = {
        id: consultationId || `consultation_${Date.now()}`,
        client: selectedClient,
        diagnosis: diagnosisData,
        colorGoal,
        formula,
        hairImages,
        referenceImages,
        timestamp: new Date().toISOString(),
        status: "completed",
        notes: consultationNotes,
      };

      // Guardar consulta completa
      await AsyncStorage.setItem(
        `consultation_${consultationData.id}`,
        JSON.stringify(consultationData)
      );

      // Actualizar historial del cliente
      await updateClientHistory(selectedClient.id, consultationData);

      // Guardar en lista de consultas
      await addToConsultationsList(consultationData);

      Alert.alert(
        "Consulta Completada",
        "La consulta de color ha sido guardada exitosamente en el historial del cliente.",
        [
          {
            text: "Ver Historial",
            onPress: () => {
              onComplete(consultationData);
              router.push("/components/ClientManagement");
            }
          },
          {
            text: "Nueva Consulta",
            onPress: () => {
              onComplete(consultationData);
              // Reset para nueva consulta
              setCurrentStep(1);
              setHairImages([]);
              setReferenceImages([]);
              setDiagnosisData(null);
              setColorGoal("");
              setFormula(null);
              setConsultationNotes("");
            }
          }
        ]
      );

    } catch (error) {
      console.error("Error completing consultation:", error);
      Alert.alert("Error", "No se pudo guardar la consulta. Inténtalo de nuevo.");
    } finally {
      setIsLoading(false);
    }
  };

  const updateClientHistory = async (clientId: string, consultation: ConsultationData) => {
    try {
      const historyKey = `client_history_${clientId}`;
      const existingHistory = await AsyncStorage.getItem(historyKey);
      const history = existingHistory ? JSON.parse(existingHistory) : [];

      history.unshift({
        id: consultation.id,
        date: consultation.timestamp,
        type: "color_consultation",
        summary: `Consulta de color: ${consultation.colorGoal || "Objetivo personalizado"}`,
        diagnosis: consultation.diagnosis,
        formula: consultation.formula,
      });

      await AsyncStorage.setItem(historyKey, JSON.stringify(history));
    } catch (error) {
      console.error("Error updating client history:", error);
    }
  };

  const addToConsultationsList = async (consultation: ConsultationData) => {
    try {
      const consultationsKey = "all_consultations";
      const existing = await AsyncStorage.getItem(consultationsKey);
      const consultations = existing ? JSON.parse(existing) : [];

      consultations.unshift({
        id: consultation.id,
        clientId: consultation.client.id,
        clientName: consultation.client.name,
        date: consultation.timestamp,
        status: consultation.status,
        colorGoal: consultation.colorGoal,
      });

      await AsyncStorage.setItem(consultationsKey, JSON.stringify(consultations));
    } catch (error) {
      console.error("Error adding to consultations list:", error);
    }
  };

  const renderStepIndicator = () => (
    <View className="flex-row justify-between items-center mb-6 px-4 pt-2">
      {[1, 2, 3, 4].map((step) => (
        <View key={step} className="items-center">
          <View
            className={`w-8 h-8 rounded-full items-center justify-center ${currentStep >= step ? "bg-blue-500" : "bg-gray-300"}`}
          >
            {currentStep > step ? (
              <Check size={16} color="white" />
            ) : (
              <Text className="text-white font-bold">{step}</Text>
            )}
          </View>
          <Text className="text-xs mt-1 text-gray-600">
            {step === 1
              ? "Diagnosis"
              : step === 2
                ? "Results"
                : step === 3
                  ? "Goal"
                  : "Formula"}
          </Text>
        </View>
      ))}
    </View>
  );

  const renderStep1 = () => (
    <View className="flex-1">
      <View className="bg-white rounded-lg p-4 mb-4 shadow-sm">
        <Text className="text-lg font-bold mb-2">Client</Text>
        <View className="flex-row items-center">
          {selectedClient.image ? (
            <Image
              source={{ uri: selectedClient.image }}
              className="w-12 h-12 rounded-full bg-gray-200"
            />
          ) : (
            <View className="w-12 h-12 rounded-full bg-gray-200" />
          )}
          <View className="ml-3">
            <Text className="font-semibold">{selectedClient.name}</Text>
            {selectedClient.lastVisit && (
              <Text className="text-gray-500 text-sm">
                Last visit: {selectedClient.lastVisit}
              </Text>
            )}
          </View>
        </View>
      </View>

      <View className="bg-white rounded-lg p-4 mb-4 shadow-sm">
        <Text className="text-lg font-bold mb-2">AI Hair Diagnosis</Text>
        <Text className="text-gray-600 mb-4">
          Take multiple photos of the client's hair from different angles for
          accurate AI analysis. Our AI will automatically analyze natural level,
          undertone, porosity, and condition. Faces are automatically blurred
          for privacy.
        </Text>

        {hairImages.length > 0 && (
          <View className="bg-blue-50 p-3 rounded-lg mb-4">
            <View className="flex-row items-center mb-2">
              <Shield size={16} color="#1e40af" />
              <Text className="text-blue-800 font-medium ml-1">
                Privacy Protected - AI Analysis Tips:
              </Text>
            </View>
            <Text className="text-blue-700 text-sm">
              •{" "}
              {hairImages.length >= 3
                ? "✓ Excellent"
                : hairImages.length >= 2
                  ? "✓ Good"
                  : "⚠ Minimum"}{" "}
              image count for analysis
            </Text>
            <Text className="text-blue-700 text-sm">
              • Include root area, mid-lengths, and ends for best results
            </Text>
            <Text className="text-blue-700 text-sm">
              • Natural lighting provides most accurate color analysis
            </Text>
            <Text className="text-blue-700 text-sm">
              • ✓ Faces automatically blurred for privacy protection
            </Text>
          </View>
        )}

        <View className="bg-green-50 p-3 rounded-lg mb-4">
          <View className="flex-row items-center mb-1">
            <ImageIcon size={16} color="#059669" />
            <Text className="text-green-800 font-medium ml-1">
              Photo Capture Options:
            </Text>
          </View>
          <Text className="text-green-700 text-sm">
            • Take new photos with camera for real-time analysis
          </Text>
          <Text className="text-green-700 text-sm">
            • Select existing photos from your gallery
          </Text>
          <Text className="text-green-700 text-sm">
            • All photos are processed with automatic face blurring
          </Text>
        </View>

        <View className="flex-row flex-wrap">
          {hairImages.map((image, index) => (
            <View key={index} className="w-1/3 aspect-square p-1">
              <Image
                source={{ uri: image }}
                className="w-full h-full rounded-md"
              />
              <TouchableOpacity
                className="absolute top-2 right-2 bg-red-500 rounded-full p-1"
                onPress={() =>
                  setHairImages(hairImages.filter((_, i) => i !== index))
                }
              >
                <X size={12} color="white" />
              </TouchableOpacity>
              <View className="absolute bottom-1 left-1 bg-black bg-opacity-50 rounded px-1">
                <Text className="text-white text-xs">{index + 1}</Text>
              </View>
              <View className="absolute top-1 left-1 bg-green-500 rounded-full p-1">
                <Shield size={10} color="white" />
              </View>
            </View>
          ))}
          {hairImages.length < 5 && (
            <TouchableOpacity
              className="w-1/3 aspect-square p-1"
              onPress={handleCaptureImage}
            >
              <View className="w-full h-full rounded-md border-2 border-dashed border-blue-300 items-center justify-center bg-blue-50">
                <View className="items-center">
                  <Camera size={20} color="#3b82f6" />
                  <Text className="text-blue-600 text-xs mt-1 text-center font-medium">
                    {hairImages.length === 0
                      ? "Capture Hair"
                      : `Photo ${hairImages.length + 1}`}
                  </Text>
                  <Text className="text-blue-500 text-xs text-center">
                    Camera/Gallery
                  </Text>
                </View>
              </View>
            </TouchableOpacity>
          )}
        </View>
      </View>

      <View className="mt-auto">
        <TouchableOpacity
          className={`py-3 px-4 rounded-lg flex-row items-center justify-center ${hairImages.length > 0 ? "bg-blue-500" : "bg-gray-300"}`}
          onPress={handleAnalyzeHair}
          disabled={hairImages.length === 0}
        >
          <Text className="text-white font-semibold mr-2">
            {hairImages.length === 0
              ? "Add Photos to Analyze"
              : `Analyze ${hairImages.length} Image${hairImages.length > 1 ? "s" : ""} with AI`}
          </Text>
          <ArrowRight size={18} color="white" />
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderStep2 = () => (
    <View className="flex-1">
      <DiagnosisResults
        diagnosisData={diagnosisData || mockDiagnosisData}
        onConfirm={() => setCurrentStep(3)}
        onEdit={() => setDiagnosisData({ ...mockDiagnosisData })} // In a real app, this would open an edit form
      />
    </View>
  );

  const renderStep3 = () => (
    <View className="flex-1">
      <View className="bg-white rounded-lg p-4 mb-4 shadow-sm">
        <Text className="text-lg font-bold mb-2">Objetivo de Color</Text>
        <Text className="text-gray-600 mb-4">
          Define el resultado de color deseado. Sube imágenes de referencia o describe
          el objetivo en detalle para una formulación más precisa.
        </Text>

        <View className="mb-4">
          <View className="flex-row items-center justify-between mb-2">
            <Text className="font-semibold">Descripción del Objetivo</Text>
            <TouchableOpacity
              onPress={() => setIsEditingGoal(!isEditingGoal)}
              className="px-3 py-1 bg-blue-100 rounded-full"
            >
              <Text className="text-blue-600 text-xs font-medium">
                {isEditingGoal ? "Guardar" : "Editar"}
              </Text>
            </TouchableOpacity>
          </View>

          {isEditingGoal ? (
            <TextInput
              className="border border-gray-300 rounded-lg p-3 min-h-[100px] bg-white text-gray-800"
              multiline
              placeholder="Describe detalladamente el color deseado: tono, técnica, intensidad, etc."
              value={colorGoal}
              onChangeText={setColorGoal}
              textAlignVertical="top"
            />
          ) : (
            <TouchableOpacity
              className="border border-gray-300 rounded-lg p-3 min-h-[100px] bg-gray-50"
              onPress={() => setIsEditingGoal(true)}
            >
              <Text className={colorGoal ? "text-gray-800" : "text-gray-500"}>
                {colorGoal ||
                  "Toca para añadir una descripción detallada del resultado de color deseado..."}
              </Text>
            </TouchableOpacity>
          )}
        </View>

        {/* Sugerencias rápidas */}
        {!colorGoal && (
          <View className="mb-4">
            <Text className="font-semibold mb-2 text-sm">Sugerencias Rápidas:</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <View className="flex-row space-x-2">
                {[
                  "Balayage caramelo con raíz natural",
                  "Rubio ceniza con mechas platino",
                  "Cobrizo intenso con reflejos dorados",
                  "Castaño chocolate con babylights",
                  "Corrección de color a rubio natural"
                ].map((suggestion, index) => (
                  <TouchableOpacity
                    key={index}
                    onPress={() => setColorGoal(suggestion)}
                    className="bg-blue-50 px-3 py-2 rounded-full border border-blue-200"
                  >
                    <Text className="text-blue-700 text-xs">{suggestion}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            </ScrollView>
          </View>
        )}

        <View>
          <Text className="font-semibold mb-2">Imágenes de Referencia</Text>
          <Text className="text-gray-500 text-sm mb-3">
            Las imágenes de referencia ayudan a la IA a generar una fórmula más precisa
          </Text>
          <View className="flex-row flex-wrap">
            {referenceImages.map((image, index) => (
              <View key={index} className="w-1/3 aspect-square p-1">
                <Image
                  source={{ uri: image }}
                  className="w-full h-full rounded-md"
                />
                <TouchableOpacity
                  className="absolute top-2 right-2 bg-red-500 rounded-full p-1"
                  onPress={() =>
                    setReferenceImages(
                      referenceImages.filter((_, i) => i !== index),
                    )
                  }
                >
                  <X size={12} color="white" />
                </TouchableOpacity>
                <View className="absolute bottom-1 left-1 bg-black bg-opacity-50 rounded px-1">
                  <Text className="text-white text-xs">Ref {index + 1}</Text>
                </View>
              </View>
            ))}
            {referenceImages.length < 3 && (
              <TouchableOpacity
                className="w-1/3 aspect-square p-1"
                onPress={handleUploadReferenceImage}
              >
                <View className="w-full h-full rounded-md border-2 border-dashed border-purple-300 items-center justify-center bg-purple-50">
                  <View className="items-center">
                    <Upload size={20} color="#8b5cf6" />
                    <Text className="text-purple-600 text-xs mt-1 font-medium">
                      Referencia
                    </Text>
                    <Text className="text-purple-500 text-xs text-center">
                      Cámara/Galería
                    </Text>
                  </View>
                </View>
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* Notas adicionales */}
        <View className="mt-4">
          <Text className="font-semibold mb-2">Notas Adicionales (Opcional)</Text>
          <TextInput
            className="border border-gray-300 rounded-lg p-3 bg-white text-gray-800"
            placeholder="Preferencias del cliente, alergias, observaciones especiales..."
            value={consultationNotes}
            onChangeText={setConsultationNotes}
            multiline
            numberOfLines={2}
          />
        </View>
      </View>

      <View className="mt-auto">
        <TouchableOpacity
          className={`py-3 px-4 rounded-lg flex-row items-center justify-center ${
            (colorGoal || referenceImages.length > 0) && !isLoading
              ? "bg-blue-500"
              : "bg-gray-300"
          }`}
          onPress={handleGenerateFormula}
          disabled={(!colorGoal && referenceImages.length === 0) || isLoading}
        >
          {isLoading ? (
            <>
              <View className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
              <Text className="text-white font-semibold">Generando Fórmula...</Text>
            </>
          ) : (
            <>
              <Text className="text-white font-semibold mr-2">
                Generar Fórmula con IA
              </Text>
              <ArrowRight size={18} color="white" />
            </>
          )}
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderAnalyzingStep = () => (
    <View className="flex-1 items-center justify-center">
      <View className="bg-white rounded-lg p-8 items-center shadow-sm">
        <View className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mb-4" />
        <Text className="text-xl font-bold mb-2 text-center">
          AI Analyzing Hair...
        </Text>
        <Text className="text-gray-600 text-center mb-4">
          Our AI is processing {hairImages.length} image
          {hairImages.length > 1 ? "s" : ""} to determine:
        </Text>
        <View className="items-start">
          <Text className="text-gray-700 py-1">
            • Natural hair level (1-10 scale)
          </Text>
          <Text className="text-gray-700 py-1">
            • Undertone (warm/cool/neutral)
          </Text>
          <Text className="text-gray-700 py-1">
            • Porosity level (low/medium/high)
          </Text>
          <Text className="text-gray-700 py-1">• Overall hair condition</Text>
          <Text className="text-gray-700 py-1">
            • Texture and density analysis
          </Text>
        </View>
        <Text className="text-sm text-blue-600 mt-4 text-center">
          This usually takes 2-3 seconds...
        </Text>
      </View>
    </View>
  );

  const renderStep4 = () => (
    <View className="flex-1">
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header con información de dificultad y costo */}
        <View className="bg-white rounded-lg p-4 mb-4 shadow-sm">
          <View className="flex-row items-center justify-between mb-3">
            <Text className="text-lg font-bold">Fórmula Generada por IA</Text>
            <View className={`px-3 py-1 rounded-full ${
              formula?.difficulty === "easy" ? "bg-green-100" :
              formula?.difficulty === "medium" ? "bg-yellow-100" : "bg-red-100"
            }`}>
              <Text className={`text-xs font-medium ${
                formula?.difficulty === "easy" ? "text-green-700" :
                formula?.difficulty === "medium" ? "text-yellow-700" : "text-red-700"
              }`}>
                {formula?.difficulty === "easy" ? "Fácil" :
                 formula?.difficulty === "medium" ? "Intermedio" : "Avanzado"}
              </Text>
            </View>
          </View>

          <Text className="text-gray-600 mb-3">
            Basada en el diagnóstico capilar y objetivo de color. Revisa cuidadosamente antes de aplicar.
          </Text>

          <View className="flex-row justify-between bg-gray-50 p-3 rounded-lg">
            <View className="items-center">
              <Text className="text-2xl font-bold text-blue-600">{formula?.processingTime}</Text>
              <Text className="text-xs text-gray-500">minutos</Text>
            </View>
            <View className="items-center">
              <Text className="text-2xl font-bold text-green-600">€{formula?.estimatedCost}</Text>
              <Text className="text-xs text-gray-500">costo aprox.</Text>
            </View>
            <View className="items-center">
              <Text className="text-2xl font-bold text-purple-600">{formula?.products.length}</Text>
              <Text className="text-xs text-gray-500">productos</Text>
            </View>
          </View>
        </View>

        {/* Advertencias si las hay */}
        {formula?.warnings && formula.warnings.length > 0 && (
          <View className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
            <Text className="font-bold text-red-800 mb-2">⚠️ Advertencias Importantes</Text>
            {formula.warnings.map((warning, index) => (
              <Text key={index} className="text-red-700 text-sm mb-1">
                {warning}
              </Text>
            ))}
          </View>
        )}

        {/* Lista de productos */}
        <View className="bg-white rounded-lg p-4 mb-4 shadow-sm">
          <Text className="font-bold mb-3">Productos Necesarios</Text>
          {formula?.products.map((product, index) => (
            <View
              key={index}
              className="flex-row justify-between items-center py-3 border-b border-gray-100"
            >
              <View className="flex-1">
                <Text className="font-medium text-gray-800">{product.name}</Text>
                {product.brand && (
                  <Text className="text-sm text-gray-500">{product.brand}</Text>
                )}
              </View>
              <View className="bg-blue-50 px-3 py-1 rounded-full">
                <Text className="font-bold text-blue-700">{product.amount}</Text>
              </View>
            </View>
          ))}
        </View>

        {/* Instrucciones paso a paso */}
        <View className="bg-white rounded-lg p-4 mb-4 shadow-sm">
          <Text className="font-bold mb-3">Instrucciones Paso a Paso</Text>
          <View className="bg-gray-50 p-3 rounded-lg">
            <Text className="text-gray-700 leading-6">{formula?.instructions}</Text>
          </View>
        </View>

        {/* Cuidados posteriores */}
        <View className="bg-white rounded-lg p-4 mb-4 shadow-sm">
          <Text className="font-bold mb-3">Recomendaciones de Cuidado</Text>
          <View className="bg-blue-50 p-3 rounded-lg">
            {formula?.aftercare.map((item, index) => (
              <View key={index} className="flex-row items-start py-1">
                <Text className="text-blue-600 mr-2">•</Text>
                <Text className="text-blue-800 flex-1">{item}</Text>
              </View>
            ))}
          </View>
        </View>

        {/* Información del diagnóstico */}
        <View className="bg-white rounded-lg p-4 mb-4 shadow-sm">
          <Text className="font-bold mb-3">Resumen del Diagnóstico</Text>
          <View className="grid grid-cols-2 gap-2">
            <View className="bg-gray-50 p-2 rounded">
              <Text className="text-xs text-gray-500">Nivel Natural</Text>
              <Text className="font-medium">{diagnosisData?.naturalLevel}</Text>
            </View>
            <View className="bg-gray-50 p-2 rounded">
              <Text className="text-xs text-gray-500">Subtono</Text>
              <Text className="font-medium">{diagnosisData?.undertone}</Text>
            </View>
            <View className="bg-gray-50 p-2 rounded">
              <Text className="text-xs text-gray-500">Porosidad</Text>
              <Text className="font-medium">{diagnosisData?.porosity}</Text>
            </View>
            <View className="bg-gray-50 p-2 rounded">
              <Text className="text-xs text-gray-500">Condición</Text>
              <Text className="font-medium">{diagnosisData?.condition}</Text>
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Botones de acción */}
      <View className="flex-row mt-4">
        <TouchableOpacity
          className="flex-1 py-3 px-4 rounded-lg flex-row items-center justify-center bg-gray-200 mr-2"
          onPress={() => setCurrentStep(3)}
          disabled={isLoading}
        >
          <RefreshCw size={18} color="#4b5563" />
          <Text className="text-gray-700 font-semibold ml-2">Refinar</Text>
        </TouchableOpacity>

        <TouchableOpacity
          className="flex-1 py-3 px-4 rounded-lg flex-row items-center justify-center bg-blue-500 mx-1"
          onPress={saveDraft}
          disabled={isLoading}
        >
          <Save size={18} color="white" />
          <Text className="text-white font-semibold ml-2">Guardar</Text>
        </TouchableOpacity>

        <TouchableOpacity
          className={`flex-1 py-3 px-4 rounded-lg flex-row items-center justify-center ml-2 ${
            isLoading ? "bg-gray-300" : "bg-green-500"
          }`}
          onPress={handleComplete}
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <View className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
              <Text className="text-white font-semibold">Guardando...</Text>
            </>
          ) : (
            <>
              <Text className="text-white font-semibold mr-2">Completar</Text>
              <Check size={18} color="white" />
            </>
          )}
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <View className="flex-1 bg-gray-50">
      {/* Header */}
      <View className="bg-white border-b border-gray-200 px-4 py-3">
        <View className="flex-row items-center justify-between">
          <TouchableOpacity onPress={() => router.back()}>
            <ArrowLeft size={24} color="#374151" />
          </TouchableOpacity>
          <Text className="text-xl font-bold text-gray-800">Consulta de Color</Text>
          <TouchableOpacity onPress={saveDraft}>
            <Save size={24} color="#6B7280" />
          </TouchableOpacity>
        </View>

        {/* Cliente info */}
        <View className="flex-row items-center mt-3 bg-gray-50 p-3 rounded-lg">
          {selectedClient.image ? (
            <Image
              source={{ uri: selectedClient.image }}
              className="w-10 h-10 rounded-full bg-gray-200"
            />
          ) : (
            <View className="w-10 h-10 rounded-full bg-gray-200" />
          )}
          <View className="ml-3 flex-1">
            <Text className="font-semibold text-gray-800">{selectedClient.name}</Text>
            {selectedClient.lastVisit && (
              <Text className="text-gray-500 text-sm">
                Última visita: {selectedClient.lastVisit}
              </Text>
            )}
          </View>
          {consultationId && (
            <View className="bg-blue-100 px-2 py-1 rounded-full">
              <Text className="text-blue-700 text-xs font-medium">Editando</Text>
            </View>
          )}
        </View>
      </View>

      {renderStepIndicator()}

      <View className="flex-1 px-4">
        {currentStep === 1 && renderStep1()}
        {currentStep === 1.5 && renderAnalyzingStep()}
        {currentStep === 2 && renderStep2()}
        {currentStep === 3 && renderStep3()}
        {currentStep === 4 && renderStep4()}
      </View>
    </View>
  );
};

export default ColorConsultation;
