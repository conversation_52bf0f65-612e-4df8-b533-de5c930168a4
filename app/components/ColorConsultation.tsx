import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Image,
  Alert,
} from "react-native";
import {
  Camera,
  ArrowRight,
  Check,
  X,
  Upload,
  RefreshCw,
  ImageIcon,
  Shield,
} from "lucide-react-native";
import { useRouter } from "expo-router";
import * as ImagePicker from "expo-image-picker";
import * as MediaLibrary from "expo-media-library";
import DiagnosisResults from "./DiagnosisResults";

interface ColorConsultationProps {
  onComplete?: (data: any) => void;
  selectedClient?: {
    id: string;
    name: string;
    image?: string;
    lastVisit?: string;
  };
}

const ColorConsultation = ({
  onComplete = () => {},
  selectedClient = {
    id: "1",
    name: "<PERSON> <PERSON>",
    image: "https://api.dicebear.com/7.x/avataaars/svg?seed=Ana",
    lastVisit: "15 Oct 2023",
  },
}: ColorConsultationProps) => {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const [hairImages, setHairImages] = useState<string[]>([]);
  const [referenceImages, setReferenceImages] = useState<string[]>([]);
  const [diagnosisData, setDiagnosisData] = useState(null);
  const [colorGoal, setColorGoal] = useState("");
  const [formula, setFormula] = useState(null);

  // Mock data for diagnosis results
  const mockDiagnosisData = {
    naturalLevel: 6,
    undertone: "Warm",
    porosity: "Medium",
    condition: "Good",
    canasPercentage: 20,
    thickness: "Medium",
    density: "High",
    elasticity: "Normal",
    previousTreatments: ["Balayage (3 months ago)", "Keratin (1 month ago)"],
  };

  // Mock data for formula
  const mockFormula = {
    products: [
      { name: "Base Color 7N", amount: "30g" },
      { name: "Developer 20 Vol", amount: "30g" },
      { name: "Toner 9V", amount: "15g" },
    ],
    instructions:
      "Apply base color to roots first. Process for 35 minutes. Rinse thoroughly. Apply toner to mid-lengths and ends. Process for 20 minutes.",
    processingTime: 55,
    aftercare: ["Hydrating Mask", "Color Protection Shampoo"],
  };

  const requestPermissions = async () => {
    const { status: cameraStatus } =
      await ImagePicker.requestCameraPermissionsAsync();
    const { status: mediaLibraryStatus } =
      await ImagePicker.requestMediaLibraryPermissionsAsync();

    if (cameraStatus !== "granted" || mediaLibraryStatus !== "granted") {
      Alert.alert(
        "Permissions Required",
        "Camera and photo library access are required to capture and select hair images for AI analysis.",
        [{ text: "OK" }],
      );
      return false;
    }
    return true;
  };

  const simulateFaceBlurring = (imageUri: string) => {
    // In a real implementation, this would use AI/ML to detect and blur faces
    // For now, we'll simulate this process and return the original image
    // The actual face blurring would happen on the server or using a local ML model
    console.log("Face blurring applied to:", imageUri);
    return imageUri;
  };

  const handleCaptureImage = async () => {
    const hasPermissions = await requestPermissions();
    if (!hasPermissions) return;

    Alert.alert(
      "Add Hair Photo",
      "Choose how you want to add a photo for AI hair analysis",
      [
        {
          text: "Take Photo",
          onPress: () => captureFromCamera(),
        },
        {
          text: "Choose from Gallery",
          onPress: () => selectFromGallery(),
        },
        {
          text: "Cancel",
          style: "cancel",
        },
      ],
    );
  };

  const captureFromCamera = async () => {
    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
        exif: false, // Remove EXIF data for privacy
      });

      if (!result.canceled && result.assets[0]) {
        const imageUri = result.assets[0].uri;
        // Apply face blurring for privacy
        const blurredImageUri = simulateFaceBlurring(imageUri);
        setHairImages([...hairImages, blurredImageUri]);

        // Show confirmation that face blurring was applied
        Alert.alert(
          "Photo Added",
          "Hair photo captured successfully. Face automatically blurred for privacy protection.",
          [{ text: "OK" }],
        );
      }
    } catch (error) {
      Alert.alert("Error", "Failed to capture image. Please try again.");
      console.error("Camera error:", error);
    }
  };

  const selectFromGallery = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
        exif: false, // Remove EXIF data for privacy
      });

      if (!result.canceled && result.assets[0]) {
        const imageUri = result.assets[0].uri;
        // Apply face blurring for privacy
        const blurredImageUri = simulateFaceBlurring(imageUri);
        setHairImages([...hairImages, blurredImageUri]);

        // Show confirmation that face blurring was applied
        Alert.alert(
          "Photo Added",
          "Hair photo selected successfully. Face automatically blurred for privacy protection.",
          [{ text: "OK" }],
        );
      }
    } catch (error) {
      Alert.alert("Error", "Failed to select image. Please try again.");
      console.error("Gallery error:", error);
    }
  };

  const handleUploadReferenceImage = async () => {
    const hasPermissions = await requestPermissions();
    if (!hasPermissions) return;

    Alert.alert(
      "Add Reference Photo",
      "Choose how you want to add a reference photo",
      [
        {
          text: "Take Photo",
          onPress: () => captureReferenceFromCamera(),
        },
        {
          text: "Choose from Gallery",
          onPress: () => selectReferenceFromGallery(),
        },
        {
          text: "Cancel",
          style: "cancel",
        },
      ],
    );
  };

  const captureReferenceFromCamera = async () => {
    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
        exif: false,
      });

      if (!result.canceled && result.assets[0]) {
        const imageUri = result.assets[0].uri;
        setReferenceImages([...referenceImages, imageUri]);
        Alert.alert(
          "Reference Photo Added",
          "Reference photo captured successfully.",
        );
      }
    } catch (error) {
      Alert.alert(
        "Error",
        "Failed to capture reference image. Please try again.",
      );
      console.error("Reference camera error:", error);
    }
  };

  const selectReferenceFromGallery = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
        exif: false,
      });

      if (!result.canceled && result.assets[0]) {
        const imageUri = result.assets[0].uri;
        setReferenceImages([...referenceImages, imageUri]);
        Alert.alert(
          "Reference Photo Added",
          "Reference photo selected successfully.",
        );
      }
    } catch (error) {
      Alert.alert(
        "Error",
        "Failed to select reference image. Please try again.",
      );
      console.error("Reference gallery error:", error);
    }
  };

  const handleAnalyzeHair = async () => {
    // Simulate AI analysis processing
    setCurrentStep(1.5); // Show loading state

    // Simulate API call delay
    await new Promise((resolve) => setTimeout(resolve, 3000));

    // Simulate AI analysis based on image characteristics
    const analysisResult = await simulateAIAnalysis(hairImages);

    setDiagnosisData(analysisResult);
    setCurrentStep(2);
  };

  const simulateAIAnalysis = async (images: string[]) => {
    // Simulate different analysis results based on number of images and mock variations
    const imageCount = images.length;
    const analysisVariations = [
      {
        naturalLevel: 4,
        undertone: "cool",
        porosity: "low" as const,
        condition: "healthy" as const,
        canasPercentage: 5,
        thickness: "Fine",
        density: "Medium",
        elasticity: "Good",
        previousTreatments: ["Virgin hair - no previous chemical treatments"],
      },
      {
        naturalLevel: 6,
        undertone: "warm",
        porosity: "medium" as const,
        condition: "normal" as const,
        canasPercentage: 20,
        thickness: "Medium",
        density: "High",
        elasticity: "Normal",
        previousTreatments: [
          "Balayage (3 months ago)",
          "Keratin (1 month ago)",
        ],
      },
      {
        naturalLevel: 8,
        undertone: "neutral",
        porosity: "high" as const,
        condition: "damaged" as const,
        canasPercentage: 35,
        thickness: "Coarse",
        density: "Low",
        elasticity: "Poor",
        previousTreatments: [
          "Bleaching (2 months ago)",
          "Permanent color (1 month ago)",
          "Chemical straightening (6 months ago)",
        ],
      },
    ];

    // Select analysis result based on image count to simulate different scenarios
    const selectedAnalysis =
      analysisVariations[
        Math.min(imageCount - 1, analysisVariations.length - 1)
      ];

    return {
      ...selectedAnalysis,
      analysisConfidence: imageCount >= 3 ? 95 : imageCount >= 2 ? 85 : 70,
      analysisNotes: [
        `Analysis based on ${imageCount} image${imageCount > 1 ? "s" : ""}`,
        imageCount >= 3
          ? "High confidence - multiple angles analyzed"
          : imageCount >= 2
            ? "Good confidence - sufficient image data"
            : "Moderate confidence - recommend additional images for better accuracy",
        "Face automatically blurred for privacy protection",
        "Hair texture and color distribution analyzed",
      ],
    };
  };

  const handleGenerateFormula = () => {
    // In a real app, this would send the diagnosis data and color goal to the AI
    // For now, we'll just set the mock formula
    setFormula(mockFormula);
    setCurrentStep(4);
  };

  const handleComplete = () => {
    onComplete({
      client: selectedClient,
      diagnosis: diagnosisData,
      colorGoal,
      formula,
      hairImages,
      referenceImages,
    });
  };

  const renderStepIndicator = () => (
    <View className="flex-row justify-between items-center mb-6 px-4 pt-2">
      {[1, 2, 3, 4].map((step) => (
        <View key={step} className="items-center">
          <View
            className={`w-8 h-8 rounded-full items-center justify-center ${currentStep >= step ? "bg-blue-500" : "bg-gray-300"}`}
          >
            {currentStep > step ? (
              <Check size={16} color="white" />
            ) : (
              <Text className="text-white font-bold">{step}</Text>
            )}
          </View>
          <Text className="text-xs mt-1 text-gray-600">
            {step === 1
              ? "Diagnosis"
              : step === 2
                ? "Results"
                : step === 3
                  ? "Goal"
                  : "Formula"}
          </Text>
        </View>
      ))}
    </View>
  );

  const renderStep1 = () => (
    <View className="flex-1">
      <View className="bg-white rounded-lg p-4 mb-4 shadow-sm">
        <Text className="text-lg font-bold mb-2">Client</Text>
        <View className="flex-row items-center">
          {selectedClient.image ? (
            <Image
              source={{ uri: selectedClient.image }}
              className="w-12 h-12 rounded-full bg-gray-200"
            />
          ) : (
            <View className="w-12 h-12 rounded-full bg-gray-200" />
          )}
          <View className="ml-3">
            <Text className="font-semibold">{selectedClient.name}</Text>
            {selectedClient.lastVisit && (
              <Text className="text-gray-500 text-sm">
                Last visit: {selectedClient.lastVisit}
              </Text>
            )}
          </View>
        </View>
      </View>

      <View className="bg-white rounded-lg p-4 mb-4 shadow-sm">
        <Text className="text-lg font-bold mb-2">AI Hair Diagnosis</Text>
        <Text className="text-gray-600 mb-4">
          Take multiple photos of the client's hair from different angles for
          accurate AI analysis. Our AI will automatically analyze natural level,
          undertone, porosity, and condition. Faces are automatically blurred
          for privacy.
        </Text>

        {hairImages.length > 0 && (
          <View className="bg-blue-50 p-3 rounded-lg mb-4">
            <View className="flex-row items-center mb-2">
              <Shield size={16} color="#1e40af" />
              <Text className="text-blue-800 font-medium ml-1">
                Privacy Protected - AI Analysis Tips:
              </Text>
            </View>
            <Text className="text-blue-700 text-sm">
              •{" "}
              {hairImages.length >= 3
                ? "✓ Excellent"
                : hairImages.length >= 2
                  ? "✓ Good"
                  : "⚠ Minimum"}{" "}
              image count for analysis
            </Text>
            <Text className="text-blue-700 text-sm">
              • Include root area, mid-lengths, and ends for best results
            </Text>
            <Text className="text-blue-700 text-sm">
              • Natural lighting provides most accurate color analysis
            </Text>
            <Text className="text-blue-700 text-sm">
              • ✓ Faces automatically blurred for privacy protection
            </Text>
          </View>
        )}

        <View className="bg-green-50 p-3 rounded-lg mb-4">
          <View className="flex-row items-center mb-1">
            <ImageIcon size={16} color="#059669" />
            <Text className="text-green-800 font-medium ml-1">
              Photo Capture Options:
            </Text>
          </View>
          <Text className="text-green-700 text-sm">
            • Take new photos with camera for real-time analysis
          </Text>
          <Text className="text-green-700 text-sm">
            • Select existing photos from your gallery
          </Text>
          <Text className="text-green-700 text-sm">
            • All photos are processed with automatic face blurring
          </Text>
        </View>

        <View className="flex-row flex-wrap">
          {hairImages.map((image, index) => (
            <View key={index} className="w-1/3 aspect-square p-1">
              <Image
                source={{ uri: image }}
                className="w-full h-full rounded-md"
              />
              <TouchableOpacity
                className="absolute top-2 right-2 bg-red-500 rounded-full p-1"
                onPress={() =>
                  setHairImages(hairImages.filter((_, i) => i !== index))
                }
              >
                <X size={12} color="white" />
              </TouchableOpacity>
              <View className="absolute bottom-1 left-1 bg-black bg-opacity-50 rounded px-1">
                <Text className="text-white text-xs">{index + 1}</Text>
              </View>
              <View className="absolute top-1 left-1 bg-green-500 rounded-full p-1">
                <Shield size={10} color="white" />
              </View>
            </View>
          ))}
          {hairImages.length < 5 && (
            <TouchableOpacity
              className="w-1/3 aspect-square p-1"
              onPress={handleCaptureImage}
            >
              <View className="w-full h-full rounded-md border-2 border-dashed border-blue-300 items-center justify-center bg-blue-50">
                <View className="items-center">
                  <Camera size={20} color="#3b82f6" />
                  <Text className="text-blue-600 text-xs mt-1 text-center font-medium">
                    {hairImages.length === 0
                      ? "Capture Hair"
                      : `Photo ${hairImages.length + 1}`}
                  </Text>
                  <Text className="text-blue-500 text-xs text-center">
                    Camera/Gallery
                  </Text>
                </View>
              </View>
            </TouchableOpacity>
          )}
        </View>
      </View>

      <View className="mt-auto">
        <TouchableOpacity
          className={`py-3 px-4 rounded-lg flex-row items-center justify-center ${hairImages.length > 0 ? "bg-blue-500" : "bg-gray-300"}`}
          onPress={handleAnalyzeHair}
          disabled={hairImages.length === 0}
        >
          <Text className="text-white font-semibold mr-2">
            {hairImages.length === 0
              ? "Add Photos to Analyze"
              : `Analyze ${hairImages.length} Image${hairImages.length > 1 ? "s" : ""} with AI`}
          </Text>
          <ArrowRight size={18} color="white" />
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderStep2 = () => (
    <View className="flex-1">
      <DiagnosisResults
        diagnosisData={diagnosisData || mockDiagnosisData}
        onConfirm={() => setCurrentStep(3)}
        onEdit={() => setDiagnosisData({ ...mockDiagnosisData })} // In a real app, this would open an edit form
      />
    </View>
  );

  const renderStep3 = () => (
    <View className="flex-1">
      <View className="bg-white rounded-lg p-4 mb-4 shadow-sm">
        <Text className="text-lg font-bold mb-2">Color Goal</Text>
        <Text className="text-gray-600 mb-4">
          Define the desired color result. Upload reference images or describe
          the goal in detail.
        </Text>

        <View className="mb-4">
          <Text className="font-semibold mb-2">Description</Text>
          <TouchableOpacity
            className="border border-gray-300 rounded-lg p-3 min-h-[100px] bg-gray-50"
            onPress={() =>
              setColorGoal(
                "Warm caramel balayage with natural root and golden highlights",
              )
            }
          >
            <Text className="text-gray-500">
              {colorGoal ||
                "Tap to add a detailed description of the desired color result..."}
            </Text>
          </TouchableOpacity>
        </View>

        <View>
          <Text className="font-semibold mb-2">Reference Images</Text>
          <View className="flex-row flex-wrap">
            {referenceImages.map((image, index) => (
              <View key={index} className="w-1/3 aspect-square p-1">
                <Image
                  source={{ uri: image }}
                  className="w-full h-full rounded-md"
                />
                <TouchableOpacity
                  className="absolute top-2 right-2 bg-red-500 rounded-full p-1"
                  onPress={() =>
                    setReferenceImages(
                      referenceImages.filter((_, i) => i !== index),
                    )
                  }
                >
                  <X size={12} color="white" />
                </TouchableOpacity>
              </View>
            ))}
            {referenceImages.length < 3 && (
              <TouchableOpacity
                className="w-1/3 aspect-square p-1"
                onPress={handleUploadReferenceImage}
              >
                <View className="w-full h-full rounded-md border-2 border-dashed border-purple-300 items-center justify-center bg-purple-50">
                  <View className="items-center">
                    <Upload size={20} color="#8b5cf6" />
                    <Text className="text-purple-600 text-xs mt-1 font-medium">
                      Reference
                    </Text>
                    <Text className="text-purple-500 text-xs text-center">
                      Camera/Gallery
                    </Text>
                  </View>
                </View>
              </TouchableOpacity>
            )}
          </View>
        </View>
      </View>

      <View className="mt-auto">
        <TouchableOpacity
          className={`py-3 px-4 rounded-lg flex-row items-center justify-center ${colorGoal || referenceImages.length > 0 ? "bg-blue-500" : "bg-gray-300"}`}
          onPress={handleGenerateFormula}
          disabled={!colorGoal && referenceImages.length === 0}
        >
          <Text className="text-white font-semibold mr-2">
            Generate Formula
          </Text>
          <ArrowRight size={18} color="white" />
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderAnalyzingStep = () => (
    <View className="flex-1 items-center justify-center">
      <View className="bg-white rounded-lg p-8 items-center shadow-sm">
        <View className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mb-4" />
        <Text className="text-xl font-bold mb-2 text-center">
          AI Analyzing Hair...
        </Text>
        <Text className="text-gray-600 text-center mb-4">
          Our AI is processing {hairImages.length} image
          {hairImages.length > 1 ? "s" : ""} to determine:
        </Text>
        <View className="items-start">
          <Text className="text-gray-700 py-1">
            • Natural hair level (1-10 scale)
          </Text>
          <Text className="text-gray-700 py-1">
            • Undertone (warm/cool/neutral)
          </Text>
          <Text className="text-gray-700 py-1">
            • Porosity level (low/medium/high)
          </Text>
          <Text className="text-gray-700 py-1">• Overall hair condition</Text>
          <Text className="text-gray-700 py-1">
            • Texture and density analysis
          </Text>
        </View>
        <Text className="text-sm text-blue-600 mt-4 text-center">
          This usually takes 2-3 seconds...
        </Text>
      </View>
    </View>
  );

  const renderStep4 = () => (
    <View className="flex-1">
      <View className="bg-white rounded-lg p-4 mb-4 shadow-sm">
        <Text className="text-lg font-bold mb-2">AI-Generated Formula</Text>
        <Text className="text-gray-600 mb-4">
          Based on the hair diagnosis and color goal, here's the recommended
          formula.
        </Text>

        <View className="mb-4">
          <Text className="font-semibold mb-2">Products</Text>
          {formula?.products.map((product, index) => (
            <View
              key={index}
              className="flex-row justify-between py-2 border-b border-gray-100"
            >
              <Text>{product.name}</Text>
              <Text className="font-semibold">{product.amount}</Text>
            </View>
          ))}
        </View>

        <View className="mb-4">
          <Text className="font-semibold mb-2">Instructions</Text>
          <Text className="text-gray-700">{formula?.instructions}</Text>
        </View>

        <View className="mb-4">
          <Text className="font-semibold mb-2">Processing Time</Text>
          <Text className="text-gray-700">
            {formula?.processingTime} minutes
          </Text>
        </View>

        <View>
          <Text className="font-semibold mb-2">Aftercare Recommendations</Text>
          <View className="bg-blue-50 p-3 rounded-md">
            {formula?.aftercare.map((item, index) => (
              <Text key={index} className="text-gray-700 py-1">
                • {item}
              </Text>
            ))}
          </View>
        </View>
      </View>

      <View className="flex-row mt-auto">
        <TouchableOpacity
          className="flex-1 py-3 px-4 rounded-lg flex-row items-center justify-center bg-gray-200 mr-2"
          onPress={() => setCurrentStep(3)}
        >
          <RefreshCw size={18} color="#4b5563" className="mr-2" />
          <Text className="text-gray-700 font-semibold">Refine</Text>
        </TouchableOpacity>
        <TouchableOpacity
          className="flex-1 py-3 px-4 rounded-lg flex-row items-center justify-center bg-green-500 ml-2"
          onPress={handleComplete}
        >
          <Text className="text-white font-semibold mr-2">Complete</Text>
          <Check size={18} color="white" />
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <View className="flex-1 bg-gray-50">
      {renderStepIndicator()}
      <ScrollView className="flex-1 px-4 pb-4">
        {currentStep === 1 && renderStep1()}
        {currentStep === 1.5 && renderAnalyzingStep()}
        {currentStep === 2 && renderStep2()}
        {currentStep === 3 && renderStep3()}
        {currentStep === 4 && renderStep4()}
      </ScrollView>
    </View>
  );
};

export default ColorConsultation;
