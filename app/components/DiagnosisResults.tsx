import React from "react";
import { View, Text, ScrollView, TouchableOpacity } from "react-native";
import { Image } from "expo-image";
import { Check, X, Edit2, AlertCircle } from "lucide-react-native";

interface DiagnosisResultsProps {
  naturalLevel?: number;
  undertone?: string;
  porosity?: "low" | "medium" | "high";
  condition?: "damaged" | "normal" | "healthy";
  hairImage?: string;
  diagnosisData?: any;
  onConfirm?: () => void;
  onEdit?: () => void;
}

const DiagnosisResults = ({
  naturalLevel,
  undertone,
  porosity,
  condition,
  hairImage = "https://images.unsplash.com/photo-1605497788044-5a32c7078486?w=800&q=80",
  diagnosisData,
  onConfirm = () => {},
  onEdit = () => {},
}: DiagnosisResultsProps) => {
  // Use diagnosisData if provided, otherwise use individual props or defaults
  const data = diagnosisData || {
    naturalLevel: naturalLevel || 6,
    undertone: undertone || "neutral",
    porosity: porosity || "medium",
    condition: condition || "normal",
    analysisConfidence: 85,
    analysisNotes: ["Analysis based on uploaded images"],
  };
  // Helper function to get color for condition indicator
  const getConditionColor = (cond: string) => {
    switch (cond) {
      case "damaged":
        return "bg-red-500";
      case "normal":
        return "bg-yellow-500";
      case "healthy":
        return "bg-green-500";
      default:
        return "bg-gray-500";
    }
  };

  // Helper function to get porosity description
  const getPorosityDescription = (poro: string) => {
    switch (poro) {
      case "low":
        return "Cuticles are tightly closed. Hair repels moisture and is resistant to chemical processes.";
      case "medium":
        return "Balanced porosity. Hair accepts moisture and chemical processes well.";
      case "high":
        return "Cuticles are open. Hair absorbs moisture quickly but can be prone to frizz and damage.";
      default:
        return "Porosity level not determined.";
    }
  };

  return (
    <View className="bg-white p-4 rounded-lg shadow-md w-full">
      <Text className="text-2xl font-bold text-center mb-2 text-purple-800">
        AI Diagnosis Results
      </Text>
      {data.analysisConfidence && (
        <View className="items-center mb-4">
          <Text className="text-sm text-gray-600">
            Analysis Confidence: {data.analysisConfidence}%
          </Text>
          <View className="w-32 h-2 bg-gray-200 rounded-full mt-1">
            <View
              className={`h-full rounded-full ${data.analysisConfidence >= 90 ? "bg-green-500" : data.analysisConfidence >= 80 ? "bg-yellow-500" : "bg-orange-500"}`}
              style={{ width: `${data.analysisConfidence}%` }}
            />
          </View>
        </View>
      )}

      <ScrollView className="w-full" showsVerticalScrollIndicator={false}>
        {/* Hair Image */}
        <View className="mb-6 items-center">
          <Image
            source={{ uri: hairImage }}
            className="w-40 h-40 rounded-full"
            contentFit="cover"
          />
          <Text className="text-xs text-gray-500 mt-2 text-center">
            AI analysis based on submitted images
          </Text>
        </View>

        {/* Results Grid */}
        <View className="mb-6">
          {/* Natural Level */}
          <View className="flex-row justify-between items-center mb-4 bg-gray-50 p-3 rounded-lg">
            <View>
              <Text className="text-sm font-medium text-gray-500">
                Natural Level
              </Text>
              <Text className="text-lg font-bold">{data.naturalLevel}/10</Text>
            </View>
            <View className="flex-row items-center">
              {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((level) => (
                <View
                  key={level}
                  className={`w-5 h-5 mx-0.5 rounded-full ${level === data.naturalLevel ? "border-2 border-purple-600" : ""}`}
                  style={{
                    backgroundColor: `rgb(${255 - level * 25}, ${255 - level * 25}, ${255 - level * 25})`,
                  }}
                />
              ))}
            </View>
          </View>

          {/* Undertone */}
          <View className="flex-row justify-between items-center mb-4 bg-gray-50 p-3 rounded-lg">
            <View>
              <Text className="text-sm font-medium text-gray-500">
                Undertone
              </Text>
              <Text className="text-lg font-bold capitalize">
                {data.undertone}
              </Text>
            </View>
            <View className="flex-row items-center">
              <View
                className={`w-6 h-6 rounded-full ${data.undertone === "warm" ? "bg-orange-300" : data.undertone === "cool" ? "bg-blue-300" : "bg-gray-300"}`}
              />
            </View>
          </View>

          {/* Porosity */}
          <View className="mb-4 bg-gray-50 p-3 rounded-lg">
            <View className="flex-row justify-between items-center">
              <View>
                <Text className="text-sm font-medium text-gray-500">
                  Porosity
                </Text>
                <Text className="text-lg font-bold capitalize">
                  {data.porosity}
                </Text>
              </View>
              <View className="flex-row items-center">
                <View className="w-24 h-4 bg-gray-200 rounded-full overflow-hidden">
                  <View
                    className={`h-full ${data.porosity === "low" ? "w-1/3 bg-blue-400" : data.porosity === "medium" ? "w-2/3 bg-green-400" : "w-full bg-red-400"}`}
                  />
                </View>
              </View>
            </View>
            <Text className="text-xs text-gray-600 mt-2">
              {getPorosityDescription(data.porosity)}
            </Text>
          </View>

          {/* Overall Condition */}
          <View className="flex-row justify-between items-center mb-4 bg-gray-50 p-3 rounded-lg">
            <View>
              <Text className="text-sm font-medium text-gray-500">
                Overall Condition
              </Text>
              <Text className="text-lg font-bold capitalize">
                {data.condition}
              </Text>
            </View>
            <View
              className={`w-4 h-4 rounded-full ${getConditionColor(data.condition)}`}
            />
          </View>

          {/* AI Analysis Notes */}
          {data.analysisNotes && data.analysisNotes.length > 0 && (
            <View className="bg-blue-50 p-3 rounded-lg mb-4">
              <Text className="font-semibold text-blue-800 mb-2">
                AI Analysis Notes:
              </Text>
              {data.analysisNotes.map((note, index) => (
                <Text key={index} className="text-blue-700 text-sm py-0.5">
                  • {note}
                </Text>
              ))}
            </View>
          )}

          {/* Warning or Notes */}
          {data.condition === "damaged" && (
            <View className="flex-row items-center bg-red-50 p-3 rounded-lg mb-4">
              <AlertCircle size={20} color="#ef4444" />
              <Text className="ml-2 text-red-700 flex-1">
                Hair shows signs of damage. Consider recommending strengthening
                treatments before chemical processing.
              </Text>
            </View>
          )}
        </View>

        {/* Action Buttons */}
        <View className="flex-row justify-between mb-4">
          <TouchableOpacity
            onPress={onEdit}
            className="flex-row items-center justify-center bg-gray-200 rounded-lg py-3 px-6"
          >
            <Edit2 size={18} color="#4b5563" />
            <Text className="ml-2 font-medium text-gray-700">Edit Results</Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={onConfirm}
            className="flex-row items-center justify-center bg-purple-600 rounded-lg py-3 px-6"
          >
            <Check size={18} color="#ffffff" />
            <Text className="ml-2 font-medium text-white">
              Confirm & Continue
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
};

export default DiagnosisResults;
